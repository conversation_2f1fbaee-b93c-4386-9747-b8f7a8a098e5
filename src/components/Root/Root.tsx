"use client";

import { type PropsWithChildren, useEffect } from "react";
import {
  initData,
  miniApp,
  useLaunchParams,
  useSignal,
} from "@telegram-apps/sdk-react";

import { useDidMount } from "@/hooks/useDidMount";
import { mockEnv } from "@/utils/mock-env";
import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";

function RootInner({ children }: PropsWithChildren) {
  const lp = useLaunchParams();
  const isDark = useSignal(miniApp.isDark);
  const initDataUser = useSignal(initData.user);

  // Log initialization data for debugging
  useEffect(() => {
    console.log("🔍 Telegram Mini App Debug Info:");
    console.log("- Launch Params:", lp);
    console.log("- Is Dark Mode:", isDark);
    console.log("- Init Data User:", initDataUser);
    try {
      console.log("- Init Data Raw:", initData.raw);
    } catch {
      console.log("- Init Data Raw: Not available (outside Telegram)");
    }
  }, [lp, isDark, initDataUser]);

  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  // Unfortunately, Telegram Mini Apps does not allow us to use all features of
  // the Server Side Rendering. That's why we are showing loader on the server
  // side.
  const didMount = useDidMount();

  // Initialize mock environment for development
  useEffect(() => {
    if (didMount && process.env.NODE_ENV === "development") {
      mockEnv().catch((error) => {
        console.warn("Failed to initialize mock environment:", error);
      });
    }
  }, [didMount]);

  return didMount ? (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  ) : (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"></div>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

"use client";

import { type PropsWithChildren, useEffect, useState } from "react";
import { retrieveLaunchParams } from "@telegram-apps/sdk-react";

import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";
import { init } from "@/core/init";

function RootInner({ children }: PropsWithChildren) {
  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Only run on client side
    if (typeof window !== "undefined") {
      try {
        const launchParams = retrieveLaunchParams();
        const { tgWebAppPlatform: platform } = launchParams;

        const debug =
          (launchParams.tgWebAppStartParam || "").includes("debug") ||
          process.env.NODE_ENV === "development";

        // Configure all application dependencies
        init({
          debug,
          eruda: false, // Disabled for now
          mockForMacOS: platform === "macos",
        });

        setIsInitialized(true);
      } catch (e) {
        console.log("Telegram SDK initialization error:", e);
        setIsInitialized(true); // Still allow rendering even if init fails
      }
    }
  }, []);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing Telegram Mini App...</p>
        </div>
      </div>
    );
  }

  return (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  );
}

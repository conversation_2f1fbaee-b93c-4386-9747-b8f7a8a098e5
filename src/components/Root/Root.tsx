"use client";

import { type PropsWithChildren, useEffect } from "react";
import {
  initData,
  miniApp,
  useLaunchParams,
  useSignal,
} from "@telegram-apps/sdk-react";

import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";

function RootInner({ children }: PropsWithChildren) {
  const lp = useLaunchParams();
  const isDark = useSignal(miniApp.isDark);
  const initDataUser = useSignal(initData.user);

  // Log initialization data for debugging
  useEffect(() => {
    console.log("🔍 Telegram Mini App Debug Info:");
    console.log("- Launch Params:", lp);
    console.log("- Is Dark Mode:", isDark);
    console.log("- Init Data User:", initDataUser);
    try {
      console.log("- Init Data Raw:", initData.raw);
    } catch {
      console.log("- Init Data Raw: Not available (outside Telegram)");
    }
  }, [lp, isDark, initDataUser]);

  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  if (typeof window == "undefined") {
    return;
  }

  return (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  );
}

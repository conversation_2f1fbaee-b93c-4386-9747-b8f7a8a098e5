"use client";

import { type PropsWithChildren, useEffect, useState } from "react";

import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";
import { init } from "@/core/init";

function RootInner({ children }: PropsWithChildren) {
  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [telegramScriptLoaded, setTelegramScriptLoaded] = useState(false);

  useEffect(() => {
    // Only run on client side
    if (typeof window !== "undefined") {
      // Wait for Telegram script to load
      const checkTelegramScript = () => {
        if (window.Telegram?.WebApp) {
          console.log("✅ Telegram script loaded");
          setTelegramScriptLoaded(true);

          // Now initialize the SDK
          init({
            debug: process.env.NODE_ENV === "development",
            eruda: false, // Disabled for now
            mockForMacOS: false, // Will be detected automatically
          })
            .then(() => {
              console.log("✅ Telegram SDK initialized");
              setIsInitialized(true);
            })
            .catch((e) => {
              console.log("Telegram SDK initialization error:", e);
              setIsInitialized(true); // Still allow rendering even if init fails
            });
        } else {
          // Check again after a short delay
          setTimeout(checkTelegramScript, 100);
        }
      };

      checkTelegramScript();
    }
  }, []);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">
            {!telegramScriptLoaded
              ? "Loading Telegram script..."
              : "Initializing Telegram Mini App..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  );
}

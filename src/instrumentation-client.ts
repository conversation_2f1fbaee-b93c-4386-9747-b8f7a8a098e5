// Client-side instrumentation for Telegram Mini Apps
import { retrieveLaunchParams } from "@telegram-apps/sdk-react";
import { init } from "./core/init";
import { mockEnv } from "./utils/mock-env";

// Initialize the mock environment first, then the SDK
mockEnv().then(() => {
  try {
    const launchParams = retrieveLaunchParams();
    const { tgWebAppPlatform: platform } = launchParams;

    const debug =
      (launchParams.tgWebAppStartParam || "").includes("debug") ||
      process.env.NODE_ENV === "development";

    // Configure all application dependencies
    init({
      debug,
      eruda: false, // Disabled for now
      mockForMacOS: platform === "macos",
    });
  } catch (e) {
    console.log("Telegram SDK initialization error:", e);
  }
});

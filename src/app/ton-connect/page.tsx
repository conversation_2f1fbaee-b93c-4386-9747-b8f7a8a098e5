'use client';

import { TonConnectButton, useTonWallet } from '@tonconnect/ui-react';

export default function TONConnectPage() {
  const wallet = useTonWallet();

  if (!wallet) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-6">
        <div className="text-center max-w-md">
          <h1 className="text-2xl font-bold mb-4">TON Connect</h1>
          <p className="text-gray-600 mb-6">
            To display the data related to the TON Connect, it is required to connect your wallet
          </p>
          <TonConnectButton />
        </div>
      </div>
    );
  }

  const {
    account: { chain, publicKey, address },
    device: { appName, appVersion, maxProtocolVersion, platform, features },
  } = wallet;

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">TON Connect</h1>
      
      {/* Wallet Info */}
      {'imageUrl' in wallet && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center space-x-4">
            <img 
              src={wallet.imageUrl} 
              alt={wallet.name}
              className="w-12 h-12 rounded-lg"
            />
            <div>
              <h2 className="text-lg font-semibold">{wallet.name}</h2>
              <p className="text-gray-600">{wallet.appName}</p>
            </div>
          </div>
          <button
            onClick={() => {
              if ('aboutUrl' in wallet) {
                window.open(wallet.aboutUrl, '_blank');
              }
            }}
            className="mt-4 text-blue-600 hover:text-blue-800 text-sm"
          >
            About wallet
          </button>
        </div>
      )}

      {/* Account Information */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Account</h3>
        <div className="space-y-3">
          <div>
            <span className="font-medium text-gray-700">Address:</span>
            <p className="text-sm font-mono bg-gray-100 p-2 rounded mt-1 break-all">
              {address}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Chain:</span>
            <span className="ml-2 text-sm">{chain}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Public Key:</span>
            <p className="text-sm font-mono bg-gray-100 p-2 rounded mt-1 break-all">
              {publicKey}
            </p>
          </div>
        </div>
      </div>

      {/* Device Information */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Device</h3>
        <div className="space-y-3">
          <div>
            <span className="font-medium text-gray-700">App Name:</span>
            <span className="ml-2 text-sm">{appName}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">App Version:</span>
            <span className="ml-2 text-sm">{appVersion}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Max Protocol Version:</span>
            <span className="ml-2 text-sm">{maxProtocolVersion}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Platform:</span>
            <span className="ml-2 text-sm">{platform}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Features:</span>
            <p className="text-sm mt-1">
              {features
                .map((f) => (typeof f === 'object' ? f.name : undefined))
                .filter((v) => v)
                .join(', ')}
            </p>
          </div>
        </div>
      </div>

      {/* Disconnect Button */}
      <div className="text-center">
        <TonConnectButton />
      </div>
    </div>
  );
}
